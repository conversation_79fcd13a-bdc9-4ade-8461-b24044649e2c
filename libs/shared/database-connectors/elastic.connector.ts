import { Client, ClientOptions } from '@elastic/elasticsearch';
import { DatabaseConfig } from './interfaces/connector.interface';
import { AbstractConnector } from './abstract-connector';

/**
 * Configuration interface for ElasticSearch connection
 */
export interface ElasticSearchConfig extends DatabaseConfig {
  node: string | string[];
  auth?: {
    username?: string;
    password?: string;
    apiKey?: string;
  };
  serverMode?: 'serverless' | 'stateful';
  index?: string;
  options?: Omit<ClientOptions, 'node' | 'auth'>;
}

/**
 * ElasticSearch Connector Class for handling elasticsearch connections
 */
export class ElasticSearchConnector extends AbstractConnector<Client, ElasticSearchConfig> {
  private static instance: ElasticSearchConnector | null = null;
  
  /**
   * Get the singleton instance of ElasticSearchConnector
   * @param config ElasticSearch connection configuration
   * @returns The ElasticSearchConnector instance
   */
  public static getInstance(config: ElasticSearchConfig): ElasticSearchConnector {
    if (!ElasticSearchConnector.instance) {
      ElasticSearchConnector.instance = new ElasticSearchConnector(config);
    }
    return ElasticSearchConnector.instance;
  }
  
  /**
   * Creates a new ElasticSearch connector instance
   * @param config ElasticSearch connection configuration
   */
  private constructor(config: ElasticSearchConfig) {
    super(config);
  }
  
  /**
   * Connect to ElasticSearch
   * @returns Promise resolving to the ElasticSearch client
   */
  async connect(): Promise<Client> {
    try {
      if (this.client) {
        return this.client;
      }
      
      // Build client options
      const clientOptions: ClientOptions = {
        node: this.config.node,
        ...this.config.options,
      };

      // Add serverless mode if specified
      if (this.config.serverMode === 'serverless') {
        (clientOptions as any).serverMode = 'serverless';
      }

      if (this.config.auth) {
        // Priority 1: API Key authentication (for Elastic Cloud)
        if (this.config.auth.apiKey) {
          clientOptions.auth = {
            apiKey: this.config.auth.apiKey,
          };
        }
        // Priority 2: Username/Password authentication (fallback)
        else if (this.config.auth.username && this.config.auth.password) {
          clientOptions.auth = {
            username: this.config.auth.username,
            password: this.config.auth.password,
          };
        }
      }
      
      // Create ElasticSearch client
      this.client = new Client(clientOptions);
      
      // Verify connection by pinging the cluster
      const pingResult = await this.client.ping();
      if (pingResult) {
        this.logDebug('ElasticSearch connected successfully');
        this.isConnectedFlag = true;
      }
      
      return this.client;
    } catch (error) {
      console.error('Failed to connect to ElasticSearch:', error);
      throw error;
    }
  }
  
  /**
   * Disconnect from ElasticSearch
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.isConnectedFlag = false;
      this.logDebug('Disconnected from ElasticSearch');
    }
  }
  
  /**
   * Perform search operation on ElasticSearch
   * @param params Search parameters
   * @returns Search results
   */
  async search<T>(params: any): Promise<T> {
    if (!this.client) {
      throw new Error('ElasticSearch client not initialized');
    }
    return this.client.search(params) as unknown as T;
  }
  
  /**
   * Index a document in ElasticSearch
   * @param params Index parameters
   * @returns Index results
   */
  async index(params: any): Promise<any> {
    if (!this.client) {
      throw new Error('ElasticSearch client not initialized');
    }
    return this.client.index(params);
  }
  
  /**
   * Update a document in ElasticSearch
   * @param params Update parameters
   * @returns Update results
   */
  async update(params: any): Promise<any> {
    if (!this.client) {
      throw new Error('ElasticSearch client not initialized');
    }
    return this.client.update(params);
  }
  
  /**
   * Delete a document from ElasticSearch
   * @param params Delete parameters
   * @returns Delete results
   */
  async delete(params: any): Promise<any> {
    if (!this.client) {
      throw new Error('ElasticSearch client not initialized');
    }
    return this.client.delete(params);
  }
}

// Singleton instance for backward compatibility
let elasticInstance: ElasticSearchConnector | null = null;

/**
 * Initialize ElasticSearch connector with configuration
 * @param config ElasticSearch connection configuration
 * @returns Promise resolving when connected
 */
export async function initElasticSearch(config: ElasticSearchConfig): Promise<Client> {
  elasticInstance = ElasticSearchConnector.getInstance(config);
  return elasticInstance.connect();
}

/**
 * Get ElasticSearch client
 * This function is used for backward compatibility
 * @param credentials Optional credentials (not used, for compatibility)
 * @returns The ElasticSearch client
 */
export function getElasticSearchDb(credentials?: any): Client | null {
  if (!elasticInstance) {
    console.warn('ElasticSearch not initialized. Call initElasticSearch first.');
    return null;
  }
  return elasticInstance.getClient();
} 