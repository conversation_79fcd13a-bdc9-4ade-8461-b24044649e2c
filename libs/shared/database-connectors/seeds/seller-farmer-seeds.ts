import { SellerModel } from '../schemas/mongo/seller.schema';
import { FarmModel } from '../schemas/mongo/farm.schema';
import { initElasticSearch } from '../elastic.connector';
import * as bcrypt from 'bcrypt';

/**
 * Seed data for sellers (who are farmers)
 * In this system, farmers are the sellers - they are the same entity
 *
 * Login Credentials:
 * - Haneef: <EMAIL> / farmer123
 * - <PERSON><PERSON>: raj<PERSON>.<EMAIL> / farmer123
 * - <PERSON><PERSON> <PERSON>: <EMAIL> / farmer123
 * - Amit Singh: <EMAIL> / farmer123
 * - Sunita Devi: <EMAIL> / farmer123
 */
export const sellerSeeds = [
  {
    sellerId: 'FARMER-001',
    personalInfo: {
      name: 'Hanee<PERSON>',
      contact: '+91-**********',
      email: '<EMAIL>',
      address: {
        country: 'INDIA',
        state: 'Andhra Pradesh',
        city: 'Narsapur',
        pincode: '534275',
        addressLine1: 'Beside Bharath gas agency',
        addressLine2: 'MAlim Street'
      }
    },
    documents: {
      identityProof: 'ID-12345',
      landOwnership: 'LAND-67890',
      certifications: ['ORGANIC-CERT-001', 'QUALITY-CERT-002']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'State Bank of India',
      ifscCode: 'SBIN0001234'
    },
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    farms: [],
    // Password: farmer123
    password: '$2b$10$pSZ7p7AH4sqYz25KK2xA3ehFXBm.irmrV/uBd1txHqwBrsUXayPpq',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    sellerId: 'FARMER-002',
    personalInfo: {
      name: 'Govind Patel',
      contact: '+91-**********',
      email: '<EMAIL>',
      address: {
        country: 'INDIA',
        state: 'Gujarat',
        city: 'Ahmedabad',
        pincode: '380001',
        addressLine1: '45, Cotton Farm Road',
        addressLine2: 'Satellite Area'
      }
    },
    documents: {
      identityProof: 'ID-45678',
      landOwnership: 'LAND-90123',
      certifications: ['ORGANIC-CERT-003']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'HDFC Bank',
      ifscCode: 'HDFC0002345'
    },
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    farms: [],
    // Password: farmer123
    password: '$2b$10$pSZ7p7AH4sqYz25KK2xA3ehFXBm.irmrV/uBd1txHqwBrsUXayPpq',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    sellerId: 'FARMER-003',
    personalInfo: {
      name: 'Rajesh Kumar',
      contact: '+91-**********',
      email: '<EMAIL>',
      address: {
        country: 'INDIA',
        state: 'Punjab',
        city: 'Ludhiana',
        pincode: '141001',
        addressLine1: 'Village Khanna',
        addressLine2: 'GT Road'
      }
    },
    documents: {
      identityProof: 'ID-78901',
      landOwnership: 'LAND-23456',
      certifications: ['WHEAT-CERT-001']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'Punjab National Bank',
      ifscCode: 'PUNB0112233'
    },
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    farms: [],
    // Password: farmer123
    password: '$2b$10$pSZ7p7AH4sqYz25KK2xA3ehFXBm.irmrV/uBd1txHqwBrsUXayPpq',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    sellerId: 'FARMER-004',
    personalInfo: {
      name: 'Priya Sharma',
      contact: '+91-**********',
      email: '<EMAIL>',
      address: {
        country: 'INDIA',
        state: 'Rajasthan',
        city: 'Jaipur',
        pincode: '302001',
        addressLine1: 'Vegetable Farm',
        addressLine2: 'Sanganer Road'
      }
    },
    documents: {
      identityProof: 'ID-34567',
      landOwnership: 'LAND-78901',
      certifications: ['ORGANIC-CERT-004', 'VEGETABLE-CERT-001']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'State Bank of India',
      ifscCode: 'SBIN0005566'
    },
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    farms: [],
    // Password: farmer123
    password: '$2b$10$pSZ7p7AH4sqYz25KK2xA3ehFXBm.irmrV/uBd1txHqwBrsUXayPpq',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    sellerId: 'FARMER-005',
    personalInfo: {
      name: 'Amit Singh',
      contact: '+91-**********',
      email: '<EMAIL>',
      address: {
        country: 'INDIA',
        state: 'Uttar Pradesh',
        city: 'Meerut',
        pincode: '250001',
        addressLine1: 'Sugar Cane Farm',
        addressLine2: 'Delhi Road'
      }
    },
    documents: {
      identityProof: 'ID-89012',
      landOwnership: 'LAND-34567',
      certifications: ['SUGARCANE-CERT-001']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'ICICI Bank',
      ifscCode: 'ICIC0009988'
    },
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    farms: [],
    // Password: farmer123
    password: '$2b$10$pSZ7p7AH4sqYz25KK2xA3ehFXBm.irmrV/uBd1txHqwBrsUXayPpq',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    sellerId: 'FARMER-006',
    personalInfo: {
      name: 'Sunita Devi',
      contact: '+91-**********',
      email: '<EMAIL>',
      address: {
        country: 'INDIA',
        state: 'Bihar',
        city: 'Patna',
        pincode: '800001',
        addressLine1: 'Paddy Field',
        addressLine2: 'Boring Road'
      }
    },
    documents: {
      identityProof: 'ID-45678',
      landOwnership: 'LAND-89012',
      certifications: ['RICE-CERT-001', 'ORGANIC-CERT-005']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'Central Bank of India',
      ifscCode: 'CBIN0003344'
    },
    status: 'ACTIVE',
    verificationStatus: 'VERIFIED',
    farms: [],
    // Password: farmer123
    password: '$2b$10$pSZ7p7AH4sqYz25KK2xA3ehFXBm.irmrV/uBd1txHqwBrsUXayPpq',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Seed data for farms
 * Each farm is associated with a farmer (seller)
 */
export const farmSeeds = [
  {
    farmId: 'FARM-001',
    sellerId: 'FARMER-001',
    name: "Haneef's Integrated Farm",
    location: {
      country: 'INDIA',
      state: 'Andhra Pradesh',
      city: 'Narsapur',
      pincode: '534275',
      addressLine1: 'Paddy Farm 1',
      addressLine2: 'Near Canal Road',
      coordinates: {
        latitude: 16.4345,
        longitude: 81.7001
      }
    },
    totalArea: 4.2, // in hectares
    soilType: 'Alluvial',
    waterSource: 'Canal',
    certifications: ['Organic Certification'],
    status: 'ACTIVE',
    infrastructure: ['Storage Shed', 'Irrigation System', 'Processing Unit'],
    plots: [],

    // Enhanced farm management fields
    currentCrops: [
      {
        cropId: 'CROP-001',
        plotId: 'PLOT-001',
        name: 'Basmati Rice',
        variety: 'Basmati 370',
        plantingDate: new Date('2024-06-15T00:00:00Z'),
        expectedHarvestDate: new Date('2024-10-30T00:00:00Z'),
        growthStage: 'GROWING',
        season: 'KHARIF'
      }
    ],

    cropRotationPlan: [
      {
        plotId: 'PLOT-001',
        season: 'KHARIF',
        year: 2024,
        plannedCrop: 'Basmati Rice',
        variety: 'Basmati 370',
        status: 'PLANTED'
      },
      {
        plotId: 'PLOT-002',
        season: 'RABI',
        year: 2024,
        plannedCrop: 'Wheat',
        variety: 'HD-2967',
        status: 'PLANNED'
      },
      {
        plotId: 'PLOT-003',
        season: 'ZAID',
        year: 2025,
        plannedCrop: 'Tomatoes',
        variety: 'Roma',
        status: 'PLANNED'
      }
    ],

    farmingPractices: {
      primaryMethod: 'INTEGRATED',
      irrigationSystems: ['Canal Irrigation', 'Drip Irrigation'],
      sustainabilityScore: 85
    }
  },
  {
    farmId: 'FARM-002',
    sellerId: 'FARMER-002',
    name: "Govind's Cotton & Oilseed Farm",
    location: {
      country: 'INDIA',
      state: 'Gujarat',
      city: 'Ahmedabad',
      pincode: '380001',
      addressLine1: 'Cotton Farm',
      addressLine2: 'Satellite Area',
      coordinates: {
        latitude: 23.0225,
        longitude: 72.5714
      }
    },
    totalArea: 6.8, // in hectares
    soilType: 'Black Cotton',
    waterSource: 'Deep Bore Well',
    certifications: ['BT Cotton Certification'],
    status: 'ACTIVE',
    infrastructure: ['Storage Facility', 'Processing Unit', 'Solar Pumps'],
    plots: [],

    // Enhanced farm management fields
    currentCrops: [
      {
        cropId: 'CROP-004',
        plotId: 'PLOT-004',
        name: 'Cotton',
        variety: 'BT Cotton',
        plantingDate: new Date('2024-05-15T00:00:00Z'),
        expectedHarvestDate: new Date('2024-11-30T00:00:00Z'),
        growthStage: 'GROWING',
        season: 'KHARIF'
      }
    ],

    cropRotationPlan: [
      {
        plotId: 'PLOT-004',
        season: 'KHARIF',
        year: 2024,
        plannedCrop: 'Cotton',
        variety: 'BT Cotton',
        status: 'PLANTED'
      },
      {
        plotId: 'PLOT-005',
        season: 'RABI',
        year: 2024,
        plannedCrop: 'Mustard',
        variety: 'Pusa Bold',
        status: 'PLANNED'
      }
    ],

    farmingPractices: {
      primaryMethod: 'CONVENTIONAL',
      irrigationSystems: ['Sprinkler Irrigation', 'Bore Well'],
      sustainabilityScore: 70
    }
  }
];

/**
 * Seed sellers (farmers) to both MongoDB and ElasticDB
 */
export const seedSellers = async (): Promise<void> => {
  try {
    // Clear existing sellers from MongoDB
    await SellerModel.deleteMany({});
    console.log('Existing sellers cleared from MongoDB');

    // Insert new sellers (farmers) into MongoDB
    await SellerModel.insertMany(sellerSeeds);
    console.log(`Successfully seeded ${sellerSeeds.length} farmers to MongoDB with encrypted passwords`);

    // Log farmer credentials for reference
    console.log('👨‍🌾 Farmer/Seller Login Credentials (all use password: farmer123):');
    console.log('  <EMAIL> (Andhra Pradesh)');
    console.log('  <EMAIL> (Gujarat)');
    console.log('  <EMAIL> (Punjab)');
    console.log('  <EMAIL> (Rajasthan)');
    console.log('  <EMAIL> (Uttar Pradesh)');
    console.log('  <EMAIL> (Bihar)');

    // Initialize ElasticDB connection and seed sellers
    await seedSellersToElasticDB();

    console.log(`Successfully seeded ${sellerSeeds.length} sellers to both MongoDB and ElasticDB`);
  } catch (error) {
    console.error('Error seeding farmers:', error);
    throw error;
  }
};

/**
 * Seed farms to both MongoDB and ElasticDB
 */
export const seedFarms = async (): Promise<void> => {
  try {
    // Clear existing farms from MongoDB
    await FarmModel.deleteMany({});
    console.log('Existing farms cleared from MongoDB');

    // Insert farms into MongoDB
    await FarmModel.insertMany(farmSeeds);
    console.log(`Seeded ${farmSeeds.length} farms to MongoDB`);

    // Update seller (farmer) references
    const farms = await FarmModel.find({});

    for (const farm of farms) {
      // Update seller's farms array
      await SellerModel.findOneAndUpdate(
        { sellerId: farm.sellerId },
        { $push: { farms: farm._id } }
      );
    }

    console.log('Updated farmer-seller references to farms.');

    // Initialize ElasticDB connection and seed farms
    await seedFarmsToElasticDB();

    console.log(`Successfully seeded ${farmSeeds.length} farms to both MongoDB and ElasticDB`);
  } catch (error) {
    console.error('Error seeding farms:', error);
    throw error;
  }
};

/**
 * Seed farms to ElasticDB with enhanced search capabilities
 */
const seedFarmsToElasticDB = async (): Promise<void> => {
  try {
    // Initialize ElasticDB connection
    const esConfig: any = {
      node: process.env['ELASTICSEARCH_NODE'] || 'https://34.47.146.9:9200',
      index: process.env['ELASTICSEARCH_INDEX'] || 'befarma',
      sslVerify: process.env['ELASTICSEARCH_SSL_VERIFY'] !== 'false'
    };

    // Configure authentication
    if (process.env['ELASTICSEARCH_API_KEY']) {
      esConfig.auth = {
        apiKey: process.env['ELASTICSEARCH_API_KEY']
      };
      if (process.env['ELASTICSEARCH_SERVERLESS'] === 'true') {
        esConfig.serverMode = 'serverless';
      }
    } else if (process.env['ELASTICSEARCH_USERNAME'] && process.env['ELASTICSEARCH_PASSWORD']) {
      esConfig.auth = {
        username: process.env['ELASTICSEARCH_USERNAME'],
        password: process.env['ELASTICSEARCH_PASSWORD']
      };
    }

    const esClient = await initElasticSearch(esConfig);
    console.log('ElasticDB connection initialized for farm seeding');

    // Get all farms from MongoDB (freshly seeded)
    const farms = await FarmModel.find().populate('sellerId');

    if (farms.length === 0) {
      console.log('No farms found in MongoDB to seed to ElasticDB');
      return;
    }

    // Create index with mapping if it doesn't exist
    const indexName = esConfig.index;
    const indexExists = await esClient.indices.exists({ index: indexName });

    if (!indexExists) {
      await esClient.indices.create({
        index: indexName,
        settings: {
          number_of_shards: 3,
          number_of_replicas: 1,
          analysis: {
            analyzer: {
              farm_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'asciifolding']
              }
            }
          }
        }
      });
      console.log(`Created ElasticDB index: ${indexName}`);
    }

    // Bulk index all farms to ElasticDB
    const bulkBody: any[] = [];

    farms.forEach(farm => {
      const document = convertFarmToElasticsearchDocument(farm);
      bulkBody.push(
        { index: { _index: indexName, _id: farm.farmId } },
        document
      );
    });

    await esClient.bulk({
      refresh: true,
      operations: bulkBody
    });

    console.log(`Successfully indexed ${farms.length} farms to ElasticDB`);
  } catch (error) {
    console.error('Error seeding farms to ElasticDB:', error);
    // Don't throw error to prevent MongoDB seeding from failing if ElasticDB is unavailable
    console.warn('Continuing with MongoDB seeding only...');
  }
};

/**
 * Convert MongoDB farm document to Elasticsearch document format
 */
const convertFarmToElasticsearchDocument = (farm: any): any => {
  const seller = farm.sellerId || {};

  return {
    id: farm._id.toString(),
    farmId: farm.farmId,
    sellerId: seller._id || farm.sellerId,
    name: farm.name,
    location: farm.location,
    totalArea: farm.totalArea,
    soilType: farm.soilType,
    waterSource: farm.waterSource,
    infrastructure: farm.infrastructure,
    certifications: farm.certifications,
    status: farm.status,

    // Enhanced farm management fields
    currentCrops: farm.currentCrops?.map((crop: any) => ({
      ...crop,
      plantingDate: crop.plantingDate.toISOString(),
      expectedHarvestDate: crop.expectedHarvestDate.toISOString()
    })) || [],

    cropRotationPlan: farm.cropRotationPlan || [],
    farmingPractices: farm.farmingPractices || {
      primaryMethod: 'CONVENTIONAL',
      irrigationSystems: [],
      sustainabilityScore: 0
    },

    // Denormalized seller information for better search
    seller: {
      sellerId: seller._id || seller.sellerId || farm.sellerId,
      name: seller.name || `Farmer ${farm.sellerId}`,
      contact: seller.contact || '',
      email: seller.email || '',
      location: seller.location || farm.location,
      certifications: seller.certifications || [],
      experienceYears: seller.experienceYears || 0
    },

    // Search and analytics metadata
    searchMetadata: {
      cropTypes: farm.currentCrops?.map((crop: any) => crop.name) || [],
      seasons: farm.currentCrops?.map((crop: any) => crop.season) || [],
      totalPlots: farm.plots?.length || 0,
      averageYield: 0, // Will be calculated from historical data
      sustainabilityRating: farm.farmingPractices?.sustainabilityScore || 0,
      popularityScore: 1.0 // Base score, will be updated based on user interactions
    },

    createdAt: farm.createdAt.toISOString(),
    updatedAt: farm.updatedAt.toISOString()
  };
};

/**
 * Seed sellers to ElasticDB with enhanced search capabilities
 */
const seedSellersToElasticDB = async (): Promise<void> => {
  try {
    // Initialize ElasticDB connection
    const esConfig: any = {
      node: process.env['ELASTICSEARCH_NODE'] || 'https://34.47.146.9:9200',
      index: process.env['ELASTICSEARCH_INDEX'] || 'befarma',
      sslVerify: process.env['ELASTICSEARCH_SSL_VERIFY'] !== 'false'
    };

    // Configure authentication
    if (process.env['ELASTICSEARCH_API_KEY']) {
      esConfig.auth = {
        apiKey: process.env['ELASTICSEARCH_API_KEY']
      };
      if (process.env['ELASTICSEARCH_SERVERLESS'] === 'true') {
        esConfig.serverMode = 'serverless';
      }
    } else if (process.env['ELASTICSEARCH_USERNAME'] && process.env['ELASTICSEARCH_PASSWORD']) {
      esConfig.auth = {
        username: process.env['ELASTICSEARCH_USERNAME'],
        password: process.env['ELASTICSEARCH_PASSWORD']
      };
    }

    const esClient = await initElasticSearch(esConfig);
    console.log('ElasticDB connection initialized for seller seeding');

    // Get all sellers from MongoDB (freshly seeded)
    const sellers = await SellerModel.find();

    if (sellers.length === 0) {
      console.log('No sellers found in MongoDB to seed to ElasticDB');
      return;
    }

    // Create index with mapping if it doesn't exist
    const indexName = esConfig.index;
    const indexExists = await esClient.indices.exists({ index: indexName });

    if (!indexExists) {
      await esClient.indices.create({
        index: indexName,
        settings: {
          number_of_shards: 3,
          number_of_replicas: 1,
          analysis: {
            analyzer: {
              seller_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'asciifolding', 'stop']
              }
            }
          }
        }
      });
      console.log(`Created ElasticDB index: ${indexName}`);
    }

    // Bulk index all sellers to ElasticDB
    const bulkBody: any[] = [];

    sellers.forEach(seller => {
      const document = convertSellerToElasticsearchDocument(seller);
      bulkBody.push(
        { index: { _index: indexName, _id: seller.sellerId } },
        document
      );
    });

    await esClient.bulk({
      refresh: true,
      operations: bulkBody
    });

    console.log(`Successfully indexed ${sellers.length} sellers to ElasticDB`);
  } catch (error) {
    console.error('Error seeding sellers to ElasticDB:', error);
    // Don't throw error to prevent MongoDB seeding from failing if ElasticDB is unavailable
    console.warn('Continuing with MongoDB seeding only...');
  }
};

/**
 * Convert MongoDB seller document to Elasticsearch document format
 */
const convertSellerToElasticsearchDocument = (seller: any): any => {
  return {
    id: seller._id.toString(),
    sellerId: seller.sellerId,
    personalInfo: seller.personalInfo,
    status: seller.status,
    verificationStatus: seller.verificationStatus,

    // Enhanced farming profile (simplified for seeding)
    farmingProfile: {
      experienceYears: 5, // Default experience
      specializations: ['Rice', 'Wheat', 'Vegetables'],
      farmingMethods: ['CONVENTIONAL'],
      certifications: seller.documents?.certifications?.map((_cert: string) => ({
        type: 'Organic',
        issuer: 'Government',
        issueDate: new Date().toISOString(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      })) || [],
      totalFarms: 1, // Will be updated when farms are linked
      totalArea: 5.0, // Default area
      sustainabilityScore: 75
    },

    // Farms will be populated when farm seeding runs
    farms: [],

    // Crop portfolio (simplified for seeding)
    cropPortfolio: {
      totalCrops: 2,
      activeCrops: 2,
      cropTypes: ['Cereal', 'Vegetable'],
      cropVarieties: ['Basmati Rice', 'Tomatoes'],
      seasonalBreakdown: {
        kharif: ['Rice'],
        rabi: ['Wheat'],
        zaid: ['Tomatoes']
      },
      organicCrops: 1,
      conventionalCrops: 1
    },

    // Business metrics (initial values)
    businessMetrics: {
      totalOrders: 0,
      totalRevenue: 0,
      averageRating: 0,
      totalReviews: 0,
      responseTime: 24,
      fulfillmentRate: 0,
      popularityScore: 1.0,
      reliabilityScore: 0.8
    },

    // Search metadata
    searchMetadata: {
      tags: ['farmer', 'seller', 'agriculture'],
      keywords: [seller.personalInfo.name, seller.personalInfo.address.city, seller.personalInfo.address.state],
      categories: ['Agriculture', 'Farming'],
      regions: [seller.personalInfo.address.state, seller.personalInfo.address.city],
      languages: ['Hindi', 'English'],
      availability: {
        status: 'AVAILABLE',
        nextAvailableDate: null
      },
      lastActiveDate: new Date().toISOString(),
      joinDate: seller.createdAt.toISOString()
    },

    createdAt: seller.createdAt.toISOString(),
    updatedAt: seller.updatedAt.toISOString()
  };
};

// Re-export seedFarmers function for backward compatibility with the API
export const seedFarmers = seedSellers;