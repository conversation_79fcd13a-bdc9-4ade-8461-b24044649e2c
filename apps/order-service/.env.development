SERVICE_NAME=order-service

# AgriTech Seller Backend Environment Configuration

# ================================
# APPLICATION CONFIGURATION
# ================================
NODE_ENV=development
PORT=3009

# ================================
# MONGODB CONFIGURATION
# ================================
# MONGO_URI=mongodb+srv://agritech:<EMAIL>/agritech
# MONGODB_URI=mongodb+srv://agritech:<EMAIL>/agritech
# MONGODB_CONNECTION_TIMEOUT=30000
# MONGODB_POOL_SIZE=10

# ================================
# ELASTICSEARCH CONFIGURATION (BEFARMA CLOUD)
# ================================
# Befarma Elasticsearch Cloud Configuration
ELASTICSEARCH_NODE=https://e8508bbc0244446db28503a5ee3e8c1d.us-central1.gcp.cloud.es.io:443
ELASTICSEARCH_API_KEY='OVhLdmRKY0JLejM3aWxPMkhvc2I6UnNVWXBnbldEZWJnVVBhVkY0MzlIZw=='
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_SERVERLESS=true

# ================================
# JWT CONFIGURATION
# ================================

# JWT Configuration
JWT_SECRET=befarma_
JWT_EXPIRES_IN=24h

# ================================
# CORS CONFIGURATION
# ================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4200
