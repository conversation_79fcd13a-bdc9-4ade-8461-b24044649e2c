import mongoose from 'mongoose';
import { ElasticSearchConnector } from '@libs/shared';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Initialize database connections
 */
export const initializeDbConnections = async (): Promise<any> => {
  try {
    // Connect to MongoDB
    const MONGO_URI = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';

    await mongoose.connect(MONGO_URI, {
      maxPoolSize: parseInt(process.env.MONGODB_POOL_SIZE || '10'),
      serverSelectionTimeoutMS: parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'),
      socketTimeoutMS: 45000,
    });

    console.log('MongoDB connected successfully to:', MONGO_URI.replace(/\/\/.*@/, '//***:***@'));

    // Add MongoDB event listeners
    mongoose.connection.on('error', (error) => {
      console.error('MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('MongoDB reconnected');
    });

    // Initialize Elasticsearch connections
    const esConfig: any = {
      node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
      index: process.env.ELASTICSEARCH_INDEX || 'befarma',
    };

    // Priority 1: API Key authentication (for Elastic Cloud)
    if (process.env.ELASTICSEARCH_API_KEY) {
      esConfig.auth = {
        apiKey: process.env.ELASTICSEARCH_API_KEY,
      };

      // Add serverless mode for Elastic Cloud
      if (process.env.ELASTICSEARCH_SERVERLESS === 'true') {
        esConfig.serverMode = 'serverless';
      }
    }
    // Priority 2: Username/Password authentication
    else if (process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD) {
      esConfig.auth = {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD,
      };
    }

    // Try to initialize Elasticsearch, but don't fail if it's not available
    let esClient = null;
    try {
      const esConnector = ElasticSearchConnector.getInstance(esConfig);
      esClient = await esConnector.connect();
      console.log('Elasticsearch connected successfully');
    } catch (error) {
      console.warn('Elasticsearch connection failed, continuing with MongoDB only:', error.message);
      // Create a mock client that logs warnings when used
      esClient = {
        search: () => { throw new Error('Elasticsearch not available'); },
        index: () => { throw new Error('Elasticsearch not available'); },
        update: () => { throw new Error('Elasticsearch not available'); },
        delete: () => { throw new Error('Elasticsearch not available'); },
        close: () => { console.log('Mock Elasticsearch client closed'); }
      };
    }

    return esClient;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

/**
 * Close database connections gracefully
 */
export const closeDbConnections = async (esClient?: any): Promise<void> => {
  try {
    // Close MongoDB connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    }

    // Close Elasticsearch connection
    if (esClient && typeof esClient.close === 'function') {
      await esClient.close();
      console.log('Elasticsearch connection closed');
    }
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
};
