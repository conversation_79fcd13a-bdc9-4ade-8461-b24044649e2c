# Farm Service

A microservice for managing farm data in the agricultural e-commerce platform.

## Overview

The Farm Service provides APIs for managing farm information, including:
- Creating, reading, updating, and deleting farm records
- Querying farms with various filters
- Searching farms using Elasticsearch
- Getting analytics on farm data

## Technologies Used

- Node.js with Express
- MongoDB for primary data storage
- Elasticsearch for search and advanced queries
- Swagger for API documentation

## Getting Started

### Prerequisites

- Node.js (v14+)
- MongoDB
- Elasticsearch

### Environment Setup

Create a `.env` file in the service root with the following variables:

```
# Server configuration
PORT=3004

# MongoDB configuration
MONGO_URI=mongodb://localhost:27017/farm-service

# Elasticsearch configuration
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_SSL_VERIFY=false
```

### Installation

1. Install dependencies:

```
npm install
```

2. Start the service:

```
npm start
```

## API Documentation

The API documentation is available via Swagger UI at `/api-docs` when the service is running.

### Key Endpoints

- `POST /api/farms` - Create a new farm
- `GET /api/farms/{farmId}` - Get a farm by ID
- `PUT /api/farms/{farmId}` - Update an existing farm
- `DELETE /api/farms/{farmId}` - Delete a farm
- `GET /api/farms` - Query farms with filters and pagination
- `GET /api/farms/search` - Search farms using Elasticsearch
- `GET /api/farms/analytics/{sellerId}` - Get farm analytics for a seller

## Data Model

The farm model captures detailed information about farms owned by sellers, including:

- Basic information (name, location)
- Physical characteristics (total area, soil type, water source)
- Infrastructure details
- Certifications
- Plot references

## Integration Points

The Farm Service integrates with:
- MongoDB for data persistence
- Elasticsearch for search functionality
- Seller Service for seller information
- Crop Service for crop data
- Admin Service for administrative functions
- Analytics Service for farm analytics

## License

This project is proprietary and confidential.

## Contact

For questions and support, please contact the development team. 