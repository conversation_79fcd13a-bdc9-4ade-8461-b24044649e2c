PORT=3001
NODE_ENV=production
SERVICE_NAME=seller-service
MONGODB_URI=mongodb+srv://agritech:<EMAIL>/agritech

# JWT Configuration
JWT_SECRET=befarma_
JWT_EXPIRES_IN=24h

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3002,http://localhost:3003,http://localhost:3004,http://localhost:3005,http://localhost:3008,http://localhost:3009,https://yourdomain.com

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Elasticsearch Configuration
ELASTICSEARCH_NODE=https://befarma-f44523.es.us-east-1.aws.elastic.cloud:443
ELASTICSEARCH_API_KEY=OVhLdmRKY0JLejM3aWxPMkhvc2I6UnNVWXBnbldEZWJnVVBhVkY0MzlIZw==
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_SERVERLESS=true
