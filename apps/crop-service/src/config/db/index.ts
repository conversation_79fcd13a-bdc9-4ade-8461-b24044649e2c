import mongoose from 'mongoose';
import { Client } from '@elastic/elasticsearch';

/**
 * MongoDB connection configuration
 */
export const connectMongoDB = async (): Promise<void> => {
  try {
    const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/crop-service';
    
    await mongoose.connect(MONGO_URI);
    
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

/**
 * Elasticsearch connection configuration
 */
export const connectElasticsearch = (): Client => {
  try {
    const ES_NODE = process.env.ELASTICSEARCH_NODE || 'http://localhost:9200';
    const ES_API_KEY = process.env.ELASTICSEARCH_API_KEY;
    const ES_INDEX = process.env.ELASTICSEARCH_INDEX || 'befarma';
    const ES_SERVERLESS = process.env.ELASTICSEARCH_SERVERLESS === 'true';

    console.log('Elasticsearch Configuration:');
    console.log('- Node:', ES_NODE);
    console.log('- Index:', ES_INDEX);
    console.log('- Serverless Mode:', ES_SERVERLESS);
    console.log('- API Key Present:', !!ES_API_KEY);
    console.log('- API Key (first 10 chars):', ES_API_KEY ? ES_API_KEY.substring(0, 10) + '...' : 'Not provided');

    const clientOptions: any = {
      node: ES_NODE,
    };

    // Priority 1: API Key authentication (for Elastic Cloud)
    if (ES_API_KEY) {
      clientOptions.auth = {
        apiKey: ES_API_KEY
      };

      // Add serverless mode for Elastic Cloud
      if (ES_SERVERLESS) {
        clientOptions.serverMode = 'serverless';
        console.log('Serverless mode enabled for Elasticsearch client');
      }

      // Add SSL/TLS configuration for cloud endpoints
      if (ES_NODE.startsWith('https://')) {
        clientOptions.tls = {
          rejectUnauthorized: true
        };
        console.log('SSL/TLS enabled for HTTPS endpoint');
      }
    }
    // Priority 2: Username/Password authentication (fallback)
    else if (process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD) {
      clientOptions.auth = {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD
      };
      console.log('Using username/password authentication');
    } else {
      console.warn('No Elasticsearch authentication configured - using anonymous access');
    }

    // Override SSL configuration if explicitly set
    if (process.env.ELASTICSEARCH_SSL_VERIFY) {
      clientOptions.tls = {
        rejectUnauthorized: process.env.ELASTICSEARCH_SSL_VERIFY === 'true'
      };
      console.log('SSL verification override:', process.env.ELASTICSEARCH_SSL_VERIFY);
    }

    // Add request timeout and retry configuration
    clientOptions.requestTimeout = parseInt(process.env.ELASTICSEARCH_REQUEST_TIMEOUT || '30000');
    clientOptions.maxRetries = parseInt(process.env.ELASTICSEARCH_MAX_RETRIES || '3');

    console.log('Creating Elasticsearch client with options:', {
      node: clientOptions.node,
      serverMode: clientOptions.serverMode,
      hasAuth: !!clientOptions.auth,
      requestTimeout: clientOptions.requestTimeout,
      maxRetries: clientOptions.maxRetries
    });

    const client = new Client(clientOptions);

    console.log('Elasticsearch client initialized successfully');
    console.log('Authentication method:', ES_API_KEY ? 'API Key' : (process.env.ELASTICSEARCH_USERNAME ? 'Username/Password' : 'Anonymous'));

    return client;
  } catch (error) {
    console.error('Elasticsearch initialization error:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
    throw error; // Don't exit process, let the caller handle the error
  }
};

/**
 * Initialize database connections
 */
export const initializeDbConnections = async (): Promise<Client> => {
  console.log('Initializing database connections...');

  // Connect to MongoDB
  await connectMongoDB();
  console.log('MongoDB connection established');

  // Connect to Elasticsearch
  let esClient: Client;
  try {
    esClient = connectElasticsearch();
    console.log('Elasticsearch client created');
  } catch (error) {
    console.error('Failed to create Elasticsearch client:', error);
    throw error;
  }

  // Test Elasticsearch connection with detailed error handling
  try {
    console.log('Testing Elasticsearch connection...');
    const result = await esClient.ping();
    console.log('Elasticsearch ping successful:', result);

    // Test authentication by checking cluster info
    try {
      const info = await esClient.info();
      console.log('Elasticsearch cluster info:', {
        name: info.name,
        cluster_name: info.cluster_name,
        version: info.version.number
      });
    } catch (infoError) {
      console.warn('Could not retrieve cluster info (might be normal for serverless):', infoError.message);
    }

    // Check if index exists
    const indexName = process.env.ELASTICSEARCH_INDEX || 'befarma';
    try {
      const indexExists = await esClient.indices.exists({ index: indexName });
      console.log(`Index '${indexName}' exists:`, indexExists);

      if (!indexExists) {
        console.log(`Index '${indexName}' does not exist - it will be created when needed`);
      }
    } catch (indexError) {
      console.warn('Could not check index existence:', indexError.message);
    }

  } catch (error) {
    console.error('Elasticsearch connection test failed:', error);
    console.error('Error details:', {
      message: error.message,
      meta: error.meta,
      statusCode: error.statusCode,
      headers: error.headers
    });

    // Check if it's an authentication error
    if (error.statusCode === 401 || error.message.includes('security_exception')) {
      console.error('AUTHENTICATION ERROR: Please check your Elasticsearch credentials');
      console.error('Current configuration:');
      console.error('- API Key present:', !!process.env.ELASTICSEARCH_API_KEY);
      console.error('- Node URL:', process.env.ELASTICSEARCH_NODE);
      console.error('- Serverless mode:', process.env.ELASTICSEARCH_SERVERLESS);
    }

    // Don't exit as ES might not be critical for all operations
    console.warn('Continuing without Elasticsearch - some features may be limited');
  }

  return esClient;
};