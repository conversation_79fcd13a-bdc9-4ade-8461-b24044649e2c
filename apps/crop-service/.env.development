PORT=3004

# AgriTech Seller Backend Environment Configuration

# ================================
# APPLICATION CONFIGURATION
# ================================
NODE_ENV=development
PORT=3004

# ================================
# MONGODB CONFIGURATION
# ================================
# MONGO_URI=mongodb+srv://agritech:<EMAIL>/agritech
MONGODB_CONNECTION_TIMEOUT=30000
MONGODB_POOL_SIZE=10

# ================================
# ELASTICSEARCH CONFIGURATION (BEFARMA CLOUD)
# ================================
# Befarma Elasticsearch Cloud Configuration
ELASTICSEARCH_NODE=https://e8508bbc0244446db28503a5ee3e8c1d.us-central1.gcp.cloud.es.io:443
ELASTICSEARCH_API_KEY='OVhLdmRKY0JLejM3aWxPMkhvc2I6UnNVWXBnbldEZWJnVVBhVkY0MzlIZw=='
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_SERVERLESS=true

# Legacy Elasticsearch Configuration (Fallback)
# ELASTICSEARCH_USERNAME=elastic
# ELASTICSEARCH_PASSWORD=changeme
# ELASTICSEARCH_SSL_VERIFY=true

# Elasticsearch Performance Settings
ELASTICSEARCH_MAX_RETRIES=3
ELASTICSEARCH_REQUEST_TIMEOUT=30000

# ================================
# SERVICE PORTS
# ================================
# Seller Service
SELLER_SERVICE_PORT=3001

# Admin Service
ADMIN_SERVICE_PORT=3002

# Analytics Service
ANALYTICS_SERVICE_PORT=3003

# Crop Service
CROP_SERVICE_PORT=3004

# Farm Service
FARM_SERVICE_PORT=3005

# Notification Service
NOTIFICATION_SERVICE_PORT=3008

# Order Service
ORDER_SERVICE_PORT=3009

# ================================
# JWT CONFIGURATION
# ================================
JWT_SECRET=befarma_
JWT_EXPIRES_IN=24h

# ================================
# CORS CONFIGURATION
# ================================
CORS_ORIGIN=http://localhost:3001,http://localhost:3002,http://localhost:3003,http://localhost:3005,http://localhost:3008,http://localhost:3009
CORS_CREDENTIALS=true

# ================================
# RATE LIMITING
# ================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ================================
# FILE UPLOAD CONFIGURATION
# ================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# ================================
# LOGGING CONFIGURATION
# ================================
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# ================================
# EXTERNAL SERVICES
# ================================
# Email Service
EMAIL_SERVICE_URL=
EMAIL_API_KEY=

# SMS Service
SMS_SERVICE_URL=
SMS_API_KEY=

# Payment Gateway
PAYMENT_GATEWAY_URL=
PAYMENT_API_KEY=

# ================================
# DEVELOPMENT CONFIGURATION
# ================================
# Enable debug mode
DEBUG=true

# Enable hot reload
HOT_RELOAD=true

# Enable API documentation
ENABLE_SWAGGER=true

# ================================
# PRODUCTION CONFIGURATION
# ================================
# SSL Configuration
SSL_CERT_PATH=
SSL_KEY_PATH=

# Security Headers
HELMET_ENABLED=true

# Compression
COMPRESSION_ENABLED=true
