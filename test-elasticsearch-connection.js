#!/usr/bin/env node

/**
 * Elasticsearch Connection Test Script
 * Tests the connection to Elasticsearch using the configured environment variables
 */

require('dotenv').config();
const { Client } = require('@elastic/elasticsearch');

async function testElasticsearchConnection() {
  console.log('🌾 AgriTech Seller Backend - Elasticsearch Connection Test');
  console.log('=========================================================');
  console.log('');

  try {
    // Read configuration from environment variables
    const ES_NODE = process.env.ELASTICSEARCH_NODE || 'http://localhost:9200';
    const ES_API_KEY = process.env.ELASTICSEARCH_API_KEY;
    const ES_INDEX = process.env.ELASTICSEARCH_INDEX || 'befarma';
    const ES_SERVERLESS = process.env.ELASTICSEARCH_SERVERLESS === 'true';

    console.log('📋 Configuration:');
    console.log(`   Node: ${ES_NODE}`);
    console.log(`   Index: ${ES_INDEX}`);
    console.log(`   Serverless: ${ES_SERVERLESS}`);
    console.log(`   Auth Method: ${ES_API_KEY ? 'API Key' : 'Username/Password'}`);
    console.log('');

    // Create Elasticsearch client
    const clientOptions = {
      node: ES_NODE,
    };

    // Configure authentication
    if (ES_API_KEY) {
      clientOptions.auth = {
        apiKey: ES_API_KEY
      };
      
      if (ES_SERVERLESS) {
        clientOptions.serverMode = 'serverless';
      }
    } else if (process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD) {
      clientOptions.auth = {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD
      };
    }

    // Add SSL configuration if needed
    if (process.env.ELASTICSEARCH_SSL_VERIFY) {
      clientOptions.tls = {
        rejectUnauthorized: process.env.ELASTICSEARCH_SSL_VERIFY === 'true'
      };
    }

    console.log('🔌 Creating Elasticsearch client...');
    const client = new Client(clientOptions);

    // Test 1: Ping the cluster
    console.log('🏓 Testing cluster ping...');
    const pingResult = await client.ping();
    console.log('✅ Ping successful!');

    // Test 2: Get cluster info
    console.log('ℹ️  Getting cluster information...');
    const clusterInfo = await client.info();
    console.log('✅ Cluster info retrieved:');
    console.log(`   Cluster Name: ${clusterInfo.cluster_name}`);
    console.log(`   Version: ${clusterInfo.version.number}`);
    console.log(`   Lucene Version: ${clusterInfo.version.lucene_version}`);

    // Test 3: Check if index exists
    console.log(`📊 Checking if index '${ES_INDEX}' exists...`);
    const indexExists = await client.indices.exists({ index: ES_INDEX });
    
    if (indexExists) {
      console.log('✅ Index exists!');
      
      // Get index stats
      const indexStats = await client.indices.stats({ index: ES_INDEX });
      const stats = indexStats.indices[ES_INDEX];
      console.log(`   Documents: ${stats.total.docs.count}`);
      console.log(`   Size: ${(stats.total.store.size_in_bytes / 1024 / 1024).toFixed(2)} MB`);
    } else {
      console.log('⚠️  Index does not exist');
      
      // Test 4: Create index with mapping
      console.log(`🔨 Creating index '${ES_INDEX}' with mapping...`);
      
      const mapping = {
        properties: {
          text: {
            type: 'text'
          },
          cropId: {
            type: 'keyword'
          },
          farmId: {
            type: 'keyword'
          },
          sellerId: {
            type: 'keyword'
          },
          name: {
            type: 'text',
            analyzer: 'standard'
          },
          type: {
            type: 'keyword'
          },
          variety: {
            type: 'text'
          },
          growthStage: {
            type: 'keyword'
          },
          healthStatus: {
            type: 'keyword'
          },
          plantingDate: {
            type: 'date'
          },
          expectedHarvestDate: {
            type: 'date'
          },
          createdAt: {
            type: 'date'
          },
          updatedAt: {
            type: 'date'
          }
        }
      };

      const createIndexResponse = await client.indices.create({
        index: ES_INDEX,
        mappings: mapping,
        settings: {
          number_of_shards: 1,
          number_of_replicas: 0,
          analysis: {
            analyzer: {
              crop_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'asciifolding']
              }
            }
          }
        }
      });

      console.log('✅ Index created successfully!');
      console.log(`   Index: ${createIndexResponse.index}`);
      console.log(`   Acknowledged: ${createIndexResponse.acknowledged}`);
    }

    // Test 5: Test document operations
    console.log('📝 Testing document operations...');
    
    const testDoc = {
      text: 'Test crop document',
      cropId: 'test-crop-001',
      farmId: 'test-farm-001',
      sellerId: 'test-seller-001',
      name: 'Test Tomato',
      type: 'Vegetable',
      variety: 'Cherry Tomato',
      growthStage: 'GROWING',
      healthStatus: 'HEALTHY',
      plantingDate: new Date().toISOString(),
      expectedHarvestDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Index test document
    const indexResponse = await client.index({
      index: ES_INDEX,
      id: 'test-doc-001',
      document: testDoc,
      refresh: true
    });

    console.log('✅ Test document indexed successfully!');
    console.log(`   Document ID: ${indexResponse._id}`);
    console.log(`   Result: ${indexResponse.result}`);

    // Search for test document
    const searchResponse = await client.search({
      index: ES_INDEX,
      query: {
        match: {
          name: 'Test Tomato'
        }
      }
    });

    console.log('✅ Search test successful!');
    console.log(`   Found ${searchResponse.hits.total.value} documents`);

    // Clean up test document
    await client.delete({
      index: ES_INDEX,
      id: 'test-doc-001',
      refresh: true
    });

    console.log('✅ Test document cleaned up');

    console.log('');
    console.log('🎉 All tests passed! Elasticsearch is properly configured and working.');
    console.log('');
    console.log('🚀 You can now start the AgriTech services:');
    console.log('   npm run start');
    console.log('');

  } catch (error) {
    console.error('');
    console.error('❌ Elasticsearch connection test failed:');
    console.error('');
    
    if (error.meta && error.meta.statusCode) {
      console.error(`   Status Code: ${error.meta.statusCode}`);
      console.error(`   Error Type: ${error.name}`);
      console.error(`   Message: ${error.message}`);
    } else {
      console.error(`   Error: ${error.message}`);
    }
    
    console.error('');
    console.error('🔧 Troubleshooting:');
    console.error('1. Check your .env file configuration');
    console.error('2. Verify your API key is correct');
    console.error('3. Ensure the Elasticsearch cluster is accessible');
    console.error('4. Run the setup script: ./setup-elasticsearch.sh');
    console.error('');
    
    process.exit(1);
  }
}

// Run the test
testElasticsearchConnection();
