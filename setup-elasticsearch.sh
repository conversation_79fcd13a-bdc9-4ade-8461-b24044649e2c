#!/bin/bash

# AgriTech Seller Backend - Elasticsearch Setup Script
# This script helps configure Elasticsearch environment variables

echo "🌾 AgriTech Seller Backend - Elasticsearch Configuration Setup"
echo "=============================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created successfully!"
else
    echo "📄 .env file already exists"
fi

echo ""
echo "🔧 Elasticsearch Configuration Options:"
echo "1. Befarma Cloud (Recommended)"
echo "2. Local Elasticsearch"
echo "3. Custom Configuration"
echo ""

read -p "Choose configuration option (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🌩️  Configuring Befarma Cloud Elasticsearch..."
        echo ""
        
        # Befarma Cloud Configuration
        ELASTICSEARCH_NODE="https://befarma-f44523.es.us-east-1.aws.elastic.cloud:443"
        ELASTICSEARCH_INDEX="befarma"
        ELASTICSEARCH_SERVERLESS="true"
        
        echo "📋 Please provide your Befarma Cloud API Key:"
        read -p "API Key: " api_key
        
        if [ -z "$api_key" ]; then
            echo "❌ API Key is required for Befarma Cloud configuration"
            exit 1
        fi
        
        # Update .env file
        sed -i "s|ELASTICSEARCH_NODE=.*|ELASTICSEARCH_NODE=$ELASTICSEARCH_NODE|g" .env
        sed -i "s|ELASTICSEARCH_API_KEY=.*|ELASTICSEARCH_API_KEY=$api_key|g" .env
        sed -i "s|ELASTICSEARCH_INDEX=.*|ELASTICSEARCH_INDEX=$ELASTICSEARCH_INDEX|g" .env
        sed -i "s|ELASTICSEARCH_SERVERLESS=.*|ELASTICSEARCH_SERVERLESS=$ELASTICSEARCH_SERVERLESS|g" .env
        
        echo ""
        echo "✅ Befarma Cloud configuration completed!"
        echo "📍 Node: $ELASTICSEARCH_NODE"
        echo "📊 Index: $ELASTICSEARCH_INDEX"
        echo "🔑 API Key: ${api_key:0:10}..."
        ;;
        
    2)
        echo ""
        echo "🏠 Configuring Local Elasticsearch..."
        echo ""
        
        # Local Configuration
        ELASTICSEARCH_NODE="http://localhost:9200"
        ELASTICSEARCH_INDEX="befarma"
        ELASTICSEARCH_SERVERLESS="false"
        ELASTICSEARCH_USERNAME="elastic"
        
        read -p "Elasticsearch Username (default: elastic): " username
        read -s -p "Elasticsearch Password: " password
        echo ""
        
        username=${username:-elastic}
        
        if [ -z "$password" ]; then
            echo "❌ Password is required for local Elasticsearch"
            exit 1
        fi
        
        # Update .env file
        sed -i "s|ELASTICSEARCH_NODE=.*|ELASTICSEARCH_NODE=$ELASTICSEARCH_NODE|g" .env
        sed -i "s|ELASTICSEARCH_INDEX=.*|ELASTICSEARCH_INDEX=$ELASTICSEARCH_INDEX|g" .env
        sed -i "s|ELASTICSEARCH_SERVERLESS=.*|ELASTICSEARCH_SERVERLESS=$ELASTICSEARCH_SERVERLESS|g" .env
        sed -i "s|# ELASTICSEARCH_USERNAME=.*|ELASTICSEARCH_USERNAME=$username|g" .env
        sed -i "s|# ELASTICSEARCH_PASSWORD=.*|ELASTICSEARCH_PASSWORD=$password|g" .env
        
        echo ""
        echo "✅ Local Elasticsearch configuration completed!"
        echo "📍 Node: $ELASTICSEARCH_NODE"
        echo "📊 Index: $ELASTICSEARCH_INDEX"
        echo "👤 Username: $username"
        ;;
        
    3)
        echo ""
        echo "⚙️  Custom Elasticsearch Configuration..."
        echo ""
        
        read -p "Elasticsearch Node URL: " node_url
        read -p "Index Name (default: befarma): " index_name
        read -p "Use API Key authentication? (y/n): " use_api_key
        
        index_name=${index_name:-befarma}
        
        if [ -z "$node_url" ]; then
            echo "❌ Node URL is required"
            exit 1
        fi
        
        # Update .env file
        sed -i "s|ELASTICSEARCH_NODE=.*|ELASTICSEARCH_NODE=$node_url|g" .env
        sed -i "s|ELASTICSEARCH_INDEX=.*|ELASTICSEARCH_INDEX=$index_name|g" .env
        
        if [[ $use_api_key =~ ^[Yy]$ ]]; then
            read -p "API Key: " api_key
            read -p "Serverless mode? (y/n): " serverless
            
            sed -i "s|ELASTICSEARCH_API_KEY=.*|ELASTICSEARCH_API_KEY=$api_key|g" .env
            
            if [[ $serverless =~ ^[Yy]$ ]]; then
                sed -i "s|ELASTICSEARCH_SERVERLESS=.*|ELASTICSEARCH_SERVERLESS=true|g" .env
            else
                sed -i "s|ELASTICSEARCH_SERVERLESS=.*|ELASTICSEARCH_SERVERLESS=false|g" .env
            fi
        else
            read -p "Username: " username
            read -s -p "Password: " password
            echo ""
            
            sed -i "s|# ELASTICSEARCH_USERNAME=.*|ELASTICSEARCH_USERNAME=$username|g" .env
            sed -i "s|# ELASTICSEARCH_PASSWORD=.*|ELASTICSEARCH_PASSWORD=$password|g" .env
            sed -i "s|ELASTICSEARCH_SERVERLESS=.*|ELASTICSEARCH_SERVERLESS=false|g" .env
        fi
        
        echo ""
        echo "✅ Custom Elasticsearch configuration completed!"
        echo "📍 Node: $node_url"
        echo "📊 Index: $index_name"
        ;;
        
    *)
        echo "❌ Invalid option selected"
        exit 1
        ;;
esac

echo ""
echo "🔧 Additional Configuration:"
echo ""

# MongoDB Configuration
read -p "MongoDB URI (default: mongodb://localhost:27017/agritech_seller): " mongo_uri
mongo_uri=${mongo_uri:-mongodb://localhost:27017/agritech_seller}
sed -i "s|MONGO_URI=.*|MONGO_URI=$mongo_uri|g" .env

echo ""
echo "🎉 Configuration completed successfully!"
echo ""
echo "📋 Summary:"
echo "- Elasticsearch configured"
echo "- MongoDB URI: $mongo_uri"
echo "- Environment file: .env"
echo ""
echo "🚀 Next steps:"
echo "1. Review the .env file"
echo "2. Start the services: npm run start"
echo "3. Test the connection: npm run test:connection"
echo ""
echo "📚 For more information, see the documentation in PROJECT_PLAN.md"
